#!/usr/bin/env python3
"""
CLI tool to test slack.py with different templates
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path


def get_default_template():
    """Returns the default Codefresh-style template"""
    return {
        "fallback": "Image push",
        "color": "good",
        "pretext": "Image pushed to repository",
        "author_name": "Test Author",
        "author_icon": "https://g.codefresh.io/modules/cf.resources/images/codefresh.png",
        "thumb_url": "https://codefresh.io/docs/assets/brand/codefresh-social-logo.png",
        "fields": [
            {
                "title": "Repository",
                "value": "test-repo",
                "short": True,
            },
            {
                "title": "Branch",
                "value": "main",
                "short": True,
            },
        ],
    }


def get_custom_template():
    """Returns a custom template example"""
    return {
        "text": "Custom message",
        "color": "warning",
        "fields": [
            {"title": "Status", "value": "Success", "short": True},
            {"title": "Environment", "value": "Production", "short": True}
        ]
    }


def run_slack_test(channel, token, message="", template_body=None, template_actions=None, template_fields=None, thread_ts=None):
    """Execute slack.py with the given parameters"""
    
    env = os.environ.copy()
    env.update({
        "SLACK_CHANNEL": channel,
        "SLACK_TOKEN": token,
        "SLACK_MESSAGE": message,
        "LOG_LEVEL": "DEBUG"
    })
    
    if template_body:
        env["SLACK_TEMPLATE_BODY"] = json.dumps(template_body)
    
    if template_actions:
        env["SLACK_TEMPLATE_ACTIONS"] = json.dumps(template_actions)
    
    if template_fields:
        env["SLACK_TEMPLATE_FIELDS"] = json.dumps(template_fields)
    
    if thread_ts:
        env["SLACK_THREAD_TS"] = thread_ts
    
    # Run slack.py
    script_path = Path(__file__).parent / "lib" / "slack.py"
    result = subprocess.run([sys.executable, str(script_path)], env=env, capture_output=True, text=True)
    
    print(f"Exit code: {result.returncode}")
    if result.stdout:
        print(f"STDOUT:\n{result.stdout}")
    if result.stderr:
        print(f"STDERR:\n{result.stderr}")


def main():
    parser = argparse.ArgumentParser(description="Test slack.py with different templates")
    parser.add_argument("--channel", required=True, help="Slack channel or email")
    parser.add_argument("--token", required=True, help="Slack token")
    parser.add_argument("--message", default="Test message", help="Message text")
    parser.add_argument("--thread-ts", help="Thread timestamp")
    
    subparsers = parser.add_subparsers(dest="template_type", help="Template types")
    
    # Simple message
    simple_parser = subparsers.add_parser("simple", help="Send simple message")
    
    # Default template (Codefresh style)
    default_parser = subparsers.add_parser("default", help="Send with default Codefresh template")
    
    # Custom template
    custom_parser = subparsers.add_parser("custom", help="Send with custom template")
    custom_parser.add_argument("--template-file", help="JSON file with template")
    custom_parser.add_argument("--actions-file", help="JSON file with actions")
    custom_parser.add_argument("--fields-file", help="JSON file with fields")
    
    # Advanced template
    advanced_parser = subparsers.add_parser("advanced", help="Send advanced template with actions")
    
    args = parser.parse_args()
    
    if not args.template_type:
        parser.print_help()
        return
    
    if args.template_type == "simple":
        print("Testing simple message...")
        run_slack_test(args.channel, args.token, args.message, thread_ts=args.thread_ts)
    
    elif args.template_type == "default":
        print("Testing default Codefresh template...")
        template = get_default_template()
        run_slack_test(args.channel, args.token, args.message, template_body=template, thread_ts=args.thread_ts)
    
    elif args.template_type == "custom":
        print("Testing custom template...")
        template = get_custom_template()
        
        if args.template_file:
            with open(args.template_file) as f:
                template = json.load(f)
        
        actions = None
        if args.actions_file:
            with open(args.actions_file) as f:
                actions = json.load(f)
        
        fields = None
        if args.fields_file:
            with open(args.fields_file) as f:
                fields = json.load(f)
        
        run_slack_test(args.channel, args.token, args.message, 
                      template_body=template, template_actions=actions, 
                      template_fields=fields, thread_ts=args.thread_ts)
    
    elif args.template_type == "advanced":
        print("Testing advanced template with actions...")
        template = {
            "text": "Deployment finished",
            "color": "good"
        }
        actions = [
            {
                "type": "button",
                "text": "View Logs",
                "url": "https://example.com/logs"
            }
        ]
        fields = [
            {"title": "Environment", "value": "Production", "short": True},
            {"title": "Version", "value": "1.2.3", "short": True}
        ]
        
        run_slack_test(args.channel, args.token, args.message,
                      template_body=template, template_actions=actions,
                      template_fields=fields, thread_ts=args.thread_ts)


if __name__ == "__main__":
    main()