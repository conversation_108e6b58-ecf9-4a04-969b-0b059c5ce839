apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.slack.0.0.3
  annotations:
    argo-hub/version: '0.0.3'
    argo-hub/description: 'A template that enables Slack notifications'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/********?v=4'
    argo-hub/owner_url: 'https://github.com/pasha-codefresh'
    argo-hub/categories: 'messaging'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/slack/assets/icon.svg"
    argo-hub/icon_background: "#49154B"
spec:
  templates:
    - name: send-message
      serviceAccountName: argo-hub.slack.0.0.3
      metadata:
        annotations:
          argo-hub-template/description: 'Send a message to a Slack channel using a hook url'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/slack/assets/icon.svg"
          argo-hub-template/icon_background: "#49154B"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        parameters:
          - name: MODE
            value: "simple"
          - name: SLACK_HOOK_URL
          - name: SLACK_TEXT
          - name: SLACK_THREAD_TS
            description: "Slack thread timestamp (optional)"
      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-slack-send-message:0.0.3-main
        command:
          - node
          - /usr/src/app/index.js
        env:
          - name: MODE
            value: '{{ inputs.parameters.MODE }}'
          - name: SLACK_HOOK_URL
            value: '{{ inputs.parameters.SLACK_HOOK_URL }}'
          - name: SLACK_TEXT
            value: '{{ inputs.parameters.SLACK_TEXT }}'
          - name: SLACK_THREAD_TS
            value: '{{ inputs.parameters.SLACK_THREAD_TS }}'
    - name: post-to-channel
      serviceAccountName: argo-hub.slack.0.0.3
      metadata:
        annotations:
          argo-hub-template/description: 'Send a message to a Slack channel using the channel name or a user email'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/slack/assets/icon.svg"
          argo-hub-template/icon_background: "#49154B"
      retryStrategy:
        limit: "3"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        parameters:
          - name: SLACK_CHANNEL
          - name: SLACK_TOKEN
            value: slack-token
          - name: SLACK_MESSAGE
            value: ""
          - name: LOG_LEVEL
            value: "info"
      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-slack-post-to-channel:0.0.3-main
        command:
          - python
          - /slack/slack.py
        env:
          - name: SLACK_CHANNEL
            value: '{{ inputs.parameters.SLACK_CHANNEL }}'
          - name: SLACK_MESSAGE
            value: '{{ inputs.parameters.SLACK_MESSAGE }}'
          - name: LOG_LEVEL
            value: '{{ inputs.parameters.LOG_LEVEL }}'
          - name: SLACK_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SLACK_TOKEN }}'
                key: token
