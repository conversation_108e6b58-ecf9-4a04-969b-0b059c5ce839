apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-hub.slack.0.0.3
  annotations:
    argo-hub/version: '0.0.3'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: argo-hub.slack.0.0.3
  annotations:
    argo-hub/version: '0.0.3'
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods/log
    verbs:
      - get
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-hub.slack.0.0.3
  annotations:
    argo-hub/version: '0.0.3'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: argo-hub.slack.0.0.3
subjects:
  - kind: ServiceAccount
    name: argo-hub.slack.0.0.3
